import { useState, useRef, useEffect } from "react";
import { FaGift, FaSearch, FaPhone, FaEnvelope, FaBuilding, FaUser, FaMoneyBillWave, FaMoneyCheckAlt, FaPercent, FaRegClock, FaCalendarTimes, FaCalendarCheck, FaDollarSign, FaUserTie, FaLayerGroup, FaHashtag, FaBoxOpen, FaCheckCircle, FaTimesCircle, FaFileExcel, FaPrint } from "react-icons/fa";
import { Dialog } from "primereact/dialog";
import { Dropdown } from "primereact/dropdown";
import { InputNumber } from "primereact/inputnumber";
import { Button } from "primereact/button";
import { Toast } from "primereact/toast";
import axios from "axios";
import { Tooltip } from "primereact/tooltip";
import CreateCardToManagerForm from "../../Backages/CreateCardToManagerForm";

// Add these imports for the animated SVGs
import { motion } from "framer-motion";
import { HiOutlineCreditCard } from "react-icons/hi";
import { BsBank2 } from "react-icons/bs";

// Import the context hook
import { useDataTableContext } from "../../../../contexts/UsersDataTableContext";
import { useGlobalContext } from "../../../../contexts/GlobalContext";
import { useLayout } from "../../../../contexts/LayoutContext";

import AssignGroupDialog from "../Groups/AssignGroupDialog";
import AddMemberDialog from "./AddMemberDialog";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Skeleton } from "primereact/skeleton";
import axiosInstance from "../../../../config/Axios";
import * as XLSX from 'xlsx'; 
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

// Define managers table config specifically for this component
const managersTableConfig = {
  url: "datatable/managers",
  filters: {
    'name': { value: '', matchMode: 'contains' },
    'email': { value: '', matchMode: 'contains' },
    'phone': { value: '', matchMode: 'contains' },
    'company_name': { value: '', matchMode: 'contains' },
    'status': { value: '', matchMode: 'contains' },
  }
};

// 1. Add import for react-beautiful-dnd at the top

// 1. استيراد dnd-kit في الأعلى
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  useDroppable,
  DragOverlay // <-- أضف هذا
} from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';

import PropTypes from 'prop-types';

function Header({ searchQuery, handleSearchChange, isMobile, createMember }) {
  const userRole = localStorage.getItem("user_role");
  return (
    <div className="w-full">
      <div className={`w-full mb-4 ${isMobile ? 'flex flex-col gap-3' : 'flex items-center'}`}>
        {/* Search Bar - Always centered */}
        <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[600px] mx-auto'}`}>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search by manager name..."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </div>
        {/* Add Manager button - Pushed to right on desktop */}
        <div className={`${isMobile ? 'w-full' : 'flex-shrink-0 ml-4'}`}>
          {userRole !== "user" && (
            <button
              className={`main-btn text-md shadow-md ${isMobile ? 'w-full' : ''}`}
              onClick={() => createMember()}
            >
              Add Manager
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

Header.propTypes = {
  searchQuery: PropTypes.string.isRequired,
  handleSearchChange: PropTypes.func.isRequired,
  isMobile: PropTypes.bool.isRequired,
  createMember: PropTypes.func.isRequired,
};

function ManagersDataTable() {
  console.log('ManagersDataTable component rendered');
  // عداد render
  const renderCount = useRef(0);
  renderCount.current += 1;
  console.log("ManagersDataTable render count:", renderCount.current);

  const {
    setLazyManagersParams,
    loading: tableLoading,
    data,
    totalRecords,
    lazyManagersParams,
  } = useDataTableContext();
  const { dialogHandler, openDialog } = useGlobalContext();
  const { isMobile } = useLayout();

  // Search state
  const [searchQuery, setSearchQuery] = useState("");
  const [allManagers, setAllManagers] = useState([]);
  const [allManagersLoading, setAllManagersLoading] = useState(false);

  console.log("data from context", data);
  console.log("searchQuery", searchQuery);

  useEffect(() => {
      fetchAllManagers();
  }, []);

  const fetchAllManagers = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/datatable/managers?all=1`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setAllManagers(response.data.data || []);
      console.log("allManagers count:", (response.data.data || []).length);
    } catch (error) {
      console.error("Error fetching all managers:", error);
    }
  };

  const handleSearchChange = (e) => setSearchQuery(e.target.value);

  const filteredData = searchQuery.trim()
    ? allManagers.filter(manager =>
        manager.name && manager.name.toLowerCase().includes(searchQuery.trim().toLowerCase())
      )
    : allManagers;

  const [selectedMember, setSelectedMember] = useState();
  const [actionType, setActionType] = useState("create"); // create or update

  // Manager detail modal state
  const [selectedManagerForModal, setSelectedManagerForModal] = useState(null);
  const [managerDetailModalVisible, setManagerDetailModalVisible] = useState(false);
  const [giftDialogVisible, setGiftDialogVisible] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [availablePackages, setAvailablePackages] = useState([]);
  const [giftLoading, setGiftLoading] = useState(false);  // تغيير اسم المتغير هنا
  const [formData, setFormData] = useState({
    user_id: null,
    package_id: null,
    duration: 12,
    payment_method: "gift",
    total_price: 0
  });
  const toastRef = useRef(null);  

  // Add state for animation
  const [animationKey, setAnimationKey] = useState(0);



  const [managerActivePackages, setManagerActivePackages] = useState(null);
  const [loadingManagerPackages, setLoadingManagerPackages] = useState(false);
  const [managerPackagesError, setManagerPackagesError] = useState(null);

  const [selectedCardForDetails, setSelectedCardForDetails] = useState(null);
  const [cardDetailsModalVisible, setCardDetailsModalVisible] = useState(false);
  const [managerCardsUsageOverride, setManagerCardsUsageOverride] = useState({});
  const [managerPackageHistory, setManagerPackageHistory] = useState([]);
  const [loadingManagerPackageHistory, setLoadingManagerPackageHistory] = useState(false);
  const [managerPackageHistoryError, setManagerPackageHistoryError] = useState(null);
const [cardsLoading, setCardsLoading] = useState(false);
const [assignLoading, setAssignLoading] = useState(false);

  const [leftCards, setLeftCards] = useState([]);
  const [rightCards, setRightCards] = useState([]);
  const [cardsFetched, setCardsFetched] = useState(false);
  // ...

  useEffect(() => {
    console.log('useEffect for cards called', { managerDetailModalVisible, cardsFetched, selectedManagerForModal });
    if (managerDetailModalVisible && !cardsFetched) {
      setCardsLoading(true);
      const fetchCards = async () => {
        try {
          const token = localStorage.getItem('token');
          const managerId = selectedManagerForModal?.id;
          if (!managerId) return;
          const response = await axios.get(
            `${import.meta.env.VITE_BACKEND_URL}/all_cards?user_id=${managerId}`,
            { headers: { Authorization: `Bearer ${token}` } }
          );
          setLeftCards(response.data || []);
          setRightCards([]);
          setCardsFetched(true);
        } catch (error) {
          setLeftCards([]);
          setRightCards([]);
        } finally {
          setCardsLoading(false);
        }
      };
      fetchCards();
    }
    if (!managerDetailModalVisible && cardsFetched) {
      setCardsFetched(false);
      setLeftCards([]);
      setRightCards([]);
      
    }
  }, [managerDetailModalVisible, cardsFetched]);

  // Drag and drop handlers


  // Assign cards API call
  const handleAssignCards = async () => {
    if (!selectedManagerForModal || rightCards.length === 0) return;
    setAssignLoading(true);
    try {
      const token = localStorage.getItem('token');
      const backendUrl = import.meta.env.VITE_BACKEND_URL;
      const requestUrl = `${backendUrl}/packages/add-cards`;
      const requestData = {
        user_id: selectedManagerForModal.id,
        card_id: rightCards.map(card => card.id),
      };
      await axios.post(requestUrl, requestData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Cards assigned successfully!',
        life: 3000,
      });
      
      setManagerActivePackages(prev => {
        if (!prev || !prev.active_packages) return prev;
        
        const updatedPackages = prev.active_packages.map(pkg => {
          if (pkg.status === 'active') {
           
            const existingIds = new Set((pkg.cards || []).map(c => c.id));
            const newCards = rightCards.filter(card => !existingIds.has(card.id));
            return {
              ...pkg,
              cards: [...(pkg.cards || []), ...newCards],
            };
          }
          return pkg;
        });
        return { ...prev, active_packages: updatedPackages };
      });
      setRightCards([]);
    } catch (error) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: error.response?.data?.message || 'Failed to assign cards',
        life: 3000,
      });
    } finally {
      setAssignLoading(false);
    }
  };


  // Pagination handlers
  const goToNextPage = () => {
    if (lazyManagersParams.first + lazyManagersParams.rows < totalRecords) {
      setLazyManagersParams(prev => ({
        ...prev,
        first: prev.first + prev.rows,
        page: prev.page + 1,
      }));
    }
  };
  const goToPrevPage = () => {
    if (lazyManagersParams.first > 0) {
      setLazyManagersParams(prev => ({
        ...prev,
        first: prev.first - prev.rows,
        page: prev.page - 1,
      }));
    }
  };
  const handleRowsPerPageChange = (e) => {
    setLazyManagersParams(prev => ({
      ...prev,
      rows: parseInt(e.target.value, 10),
      first: 0,
      page: 0,
    }));
  };

  const createMember = () => {
    setActionType("create");
    setSelectedMember({});
    dialogHandler("addMember");
  };



  const editMember = (data) => {
    setActionType("update");
    const updatedData = { ...data };
    delete updatedData.role;
    delete updatedData.group_permission;
    setSelectedMember(updatedData);
    dialogHandler("addMember");
  };

  // Delete handler with modal
  const handleDeleteClick = (rowData) => {
    confirmDialog({
      message: 'Are you sure you want to delete this manager?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      acceptLabel: 'Yes',
      rejectLabel: 'No',
      accept: () => deleteManager(rowData),
    });
  };

  // Delete logic: remove from UI and show toast
  const deleteManager = async () => {
    try {

      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Manager deleted successfully (local only)',
        life: 3000
      });
    } catch (error) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete manager',
        life: 3000
      });
    }
  };

  const fetchAvailablePackages = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/packages/original_packages`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      setAvailablePackages(response.data || []);
    } catch (error) {
      console.error("Error fetching packages:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch available packages",
        life: 3000
      });
    }
  };

  // Submit gift form
  const handleGiftSubmit = async () => {
    try {
      setGiftLoading(true);
      const token = localStorage.getItem("token");

      // For bank_transfer, we'll use the manually entered total_price directly
      // No need to recalculate it here

      await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/admin/assign-package-to-user`,
        {
          user_id: formData.user_id,
          package_id: formData.package_id,
          duration: formData.duration,
          payment_method: formData.payment_method,
          total_price: formData.payment_method === "bank_transfer" ? formData.total_price : 0
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json"
          }
        }
      );

      toastRef.current.show({
        severity: "success",
        summary: "Success",
        detail: "Package has been successfully assigned to the user",
        life: 3000
      });

      setGiftDialogVisible(false);
      // Update data - use managersTableConfig instead of usersTableConfig
      setLazyManagersParams((prev) => ({ ...prev, ...managersTableConfig }));
    } catch (error) {
      console.error("Error assigning package:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: error.response?.data?.message || "An error occurred while assigning the package",
        life: 3000
      });
    } finally {
      setGiftLoading(false);
    }
  };

  const openGiftDialog = (rowData) => {
    setFormData({
      ...formData,
      user_id: rowData.id,
      package_id: null,
      duration: 12,
      payment_method: "gift"
    });
    fetchAvailablePackages();
    setGiftDialogVisible(true);
  };



  // Manager card click handler
  const handleManagerCardClick = async (manager) => {
    if (managerDetailModalVisible) {
      return;
    }
    setSelectedManagerForModal(manager);
    setManagerDetailModalVisible(true);
    setManagerActivePackages(null);
    setManagerPackagesError(null);
    setLoadingManagerPackages(true);
    setManagerPackageHistory([]);
    setManagerPackageHistoryError(null);
    setLoadingManagerPackageHistory(true);
    try {
      const token = localStorage.getItem("token");
      // Fetch active packages (existing logic)
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/packages/active-with-cards?user_id=${manager.id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      setManagerActivePackages(response.data);
      if (response.data.active_packages && response.data.active_packages.length > 0) {
        const active = response.data.active_packages[0];
        setManagerCardsUsageOverride(prev => ({
          ...prev,
          [manager.id]: {
            cards_count: active.cards_count,
            card_limit: active.card_limit
          }
        }));
      }
      // Fetch full package history
      const historyRes = await axiosInstance.get(`/packages/${manager.id}/packages_history`);
      setManagerPackageHistory(historyRes.data.history_packages || []);
    } catch (error) {
      setManagerPackagesError(error?.response?.data?.message || "Failed to fetch active packages");
      setManagerPackageHistoryError(error?.response?.data?.message || "Failed to fetch package history");
    } finally {
      setLoadingManagerPackages(false);
      setLoadingManagerPackageHistory(false);
    }
  };

  // Manager card component
  const ManagerCard = ({ manager }) => {
    const lastPackage = manager.packages && manager.packages.length > 0
      ? manager.packages[manager.packages.length - 1]
      : null;

    // Get active package for progress bar
    const activePackage = manager.packages && manager.packages.length > 0
      ? manager.packages.find(pkg => pkg.status === 'active')
      : null;
    // استخدم override إذا توفر
    const usageOverride = managerCardsUsageOverride[manager.id];
    const cardLimit = usageOverride?.card_limit ?? activePackage?.card_limit ?? 0;
    const cardsCount = usageOverride?.cards_count ?? activePackage?.cards_count ?? 0;

    // متغير لحساب نسبة الاشتراك
    let subscriptionPercent = 0;
    if (activePackage && activePackage.purchased_at && activePackage.expiry_date) {
      const now = new Date();
      const start = new Date(activePackage.purchased_at);
      const end = new Date(activePackage.expiry_date);
      const total = end - start;
      const used = now - start;
      subscriptionPercent = total > 0 ? Math.min(100, Math.max(0, Math.round((used / total) * 100))) : 0;
    }
    const cardsBarRef = useRef(null);
    const subscriptionBarRef = useRef(null);
    const [showCardsPercent, setShowCardsPercent] = useState(false);
    const [showSubPercent, setShowSubPercent] = useState(false);

    const [cardsPercentPos, setCardsPercentPos] = useState({ x: 0, y: 0 });
    const [subPercentPos, setSubPercentPos] = useState({ x: 0, y: 0 });

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -5, scale: 1.02 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-xl shadow-lg hover:shadow-xl border border-gray-200 overflow-hidden cursor-pointer group flex flex-col h-full"
        onClick={() => handleManagerCardClick(manager)}
      >
        {/* Card Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center space-x-4">
            {/* Profile Image */}
            <div className="relative">
              <img
                src={manager.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(manager.name)}&background=00c3ac&color=fff&size=64`}
                alt={manager.name}
                className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 group-hover:border-[#00c3ac] transition-colors duration-300"
              />
            </div>
            {/* Manager Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-bold text-gray-900 truncate group-hover:text-[#00c3ac] transition-colors duration-300">
                {manager.name}
              </h3>
              <p className="text-sm text-gray-600 truncate">
                <FaBuilding className="inline mr-1" />
                {manager.company_name || 'No Company'}
              </p>
              <div style={{ display: "flex", alignItems: "center", marginTop: "0.25rem" }}>
                <span
                  style={{
                    display: "inline-flex",
                    alignItems: "center",
                    padding: "0.25rem 0.5rem",
                    borderRadius: "9999px",
                    fontSize: "0.75rem",
                    fontWeight: 500,
                    color: "#fff",
                    backgroundColor: lastPackage && lastPackage.status === "active" ? "#22C55E" : lastPackage ? "#EF4444" : "#6B7280",
                  }}
                >
                  {lastPackage ? lastPackage.status : "No Active Package"}
                </span>
              </div>
            </div>
          </div>
        </div>


        {/* Card Body - Contact Info */}
        <div className="px-6 pb-4">
          <div className="space-y-2">
            {manager.email && (
              <div className="flex items-center text-sm text-gray-600">
                <FaEnvelope className="mr-2 text-gray-400" />
                <span className="truncate">{manager.email}</span>
              </div>
            )}
            {manager.phone && (
              <div className="flex items-center text-sm text-gray-600">
                <FaPhone className="mr-2 text-gray-400" />
                <span>{manager.phone}</span>
              </div>
            )}
            {manager.department && (
              <div className="flex items-center text-sm text-gray-600">
                <FaUser className="mr-2 text-gray-400" />
                <span>{manager.department}</span>
              </div>
            )}
          </div>
        </div>

        {/* Progress Bar for cards usage */}
        {activePackage && cardLimit > 0 && (
          <div className="px-6 pb-2">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-gray-500 font-semibold">Cards Usage</span>
            </div>
            <div className="relative w-full">
              {/* الفقاعة بجانب الماوس */}
              {showCardsPercent && (
                <div
                  className="absolute z-20 flex flex-col items-center pointer-events-none select-none"
                  style={{ left: cardsPercentPos.x, top: cardsPercentPos.y - 32, transform: 'translateX(-50%)' }}
                >
                  <div className="bg-slate-800 shadow-lg border border-slate-900 rounded-xl px-2 py-0.5 text-xs font-bold text-blue-300 flex items-center gap-1 animate-fade-in-up">
                    <span>{cardLimit > 0 ? Math.round((cardsCount / cardLimit) * 100) : 0}%</span>
                  </div>
                  <div className="w-2 h-2 bg-slate-800 border-l border-b border-slate-900 rotate-45 -mt-1 shadow-sm"></div>
                </div>
              )}
              <div
                ref={cardsBarRef}
                className="w-full h-3 bg-gray-200 rounded-full overflow-hidden relative shadow-inner group/progress cursor-pointer"
                style={{ position: "relative" }}
                onMouseEnter={() => setShowCardsPercent(true)}
                onMouseLeave={() => setShowCardsPercent(false)}
                onMouseMove={e => {
                  const rect = e.currentTarget.getBoundingClientRect();
                  setCardsPercentPos({ x: e.clientX - rect.left, y: e.clientY - rect.top });
                }}
              >
                {(() => {
                  let percent = cardLimit > 0 ? Math.round((cardsCount / cardLimit) * 100) : 0;
                  let barColor = percent >= 90 ? 'linear-gradient(90deg, #ef4444 0%, #f87171 100%)' : percent >= 70 ? 'linear-gradient(90deg, #facc15 0%, #fde68a 100%)' : 'linear-gradient(90deg, #22c55e 0%, #16a34a 100%)';
                  return (
              <div
                className={`h-full transition-all duration-700 ease-in-out rounded-full relative animate-pulse`}
                style={{
                  width: `${percent}%`,
                  minWidth: percent > 0 ? 12 : 0,
                        background: barColor,
                  boxShadow: '0 2px 8px 0 rgba(0,0,0,0.10)',
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                {/* shimmer effect */}
                <div
                  className="absolute top-0 left-0 h-full w-full"
                  style={{
                    background: 'linear-gradient(120deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.35) 50%, rgba(255,255,255,0.10) 100%)',
                    animation: 'shimmer 1.8s infinite linear',
                    zIndex: 1,
                    pointerEvents: 'none',
                  }}
                />
              </div>
                  );
                })()}
                <style>{`
                  @keyframes shimmer {
                    0% { transform: translateX(-100%); }
                    100% { transform: translateX(100%); }
                  }
                `}</style>
              </div>
            </div>
            {/* Progress Bar for Subscription Duration */}
            {activePackage.purchased_at && activePackage.expiry_date && (
              <div className="mt-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-500 font-semibold">Subscription Duration</span>
                  <span className="text-xs text-blue-500 font-bold">
                    {(() => {
                      if (!activePackage.expiry_date) return '';
                      const now = new Date();
                      const end = new Date(activePackage.expiry_date);
                      const diff = Math.ceil((end - now) / (1000 * 60 * 60 * 24));
                      return diff > 0 ? `${diff} days left` : 'Expired';
                    })()}
                  </span>
                </div>
                <div className="relative w-full">
                  

                  {showSubPercent && (
                    <div
                      className="absolute z-20 flex flex-col items-center pointer-events-none select-none"
                      style={{ left: subPercentPos.x, top: subPercentPos.y - 32, transform: 'translateX(-50%)' }}
                    >
                      <div className="bg-slate-800 shadow-lg border border-slate-900 rounded-xl px-2 py-0.5 text-xs font-bold text-blue-300 flex items-center gap-1 animate-fade-in-up">
                        <span>{subscriptionPercent}%</span>
                      </div>
                      <div className="w-2 h-2 bg-slate-800 border-l border-b border-slate-900 rotate-45 -mt-1 shadow-sm"></div>
                    </div>
                  )}
                  <div
                    ref={subscriptionBarRef}
                    className="w-full h-3 bg-gray-200 rounded-full overflow-hidden relative shadow-inner group/progress cursor-pointer"
                    style={{ position: "relative" }}
                    onMouseEnter={() => setShowSubPercent(true)}
                    onMouseLeave={() => setShowSubPercent(false)}
                    onMouseMove={e => {
                      const rect = e.currentTarget.getBoundingClientRect();
                      setSubPercentPos({ x: e.clientX - rect.left, y: e.clientY - rect.top });
                    }}
                  >
                    {(() => {
                      let percent = subscriptionPercent;
                      let barColor = percent >= 90 ? 'linear-gradient(90deg, #ef4444 0%, #f87171 100%)' : percent >= 70 ? 'linear-gradient(90deg, #facc15 0%, #fde68a 100%)' : 'linear-gradient(90deg, #22c55e 0%, #16a34a 100%)';
                      return (
                        <div
                          className={`h-full transition-all duration-700 ease-in-out rounded-full relative animate-pulse`}
                style={{
                            width: `${percent}%`,
                            minWidth: percent > 0 ? 12 : 0,
                            background: barColor,
                            boxShadow: '0 2px 8px 0 rgba(0,0,0,0.10)',
                            position: 'relative',
                            overflow: 'hidden',
                          }}
                        >
                          {/* shimmer effect */}
                          <div
                            className="absolute top-0 left-0 h-full w-full"
                            style={{
                              background: 'linear-gradient(120deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.35) 50%, rgba(255,255,255,0.10) 100%)',
                              animation: 'shimmer 1.8s infinite linear',
                              zIndex: 1,
                              pointerEvents: 'none',
                            }}
                          />
                        </div>
                      );
                    })()}
              <style>{`
                @keyframes shimmer {
                  0% { transform: translateX(-100%); }
                  100% { transform: translateX(100%); }
                }
              `}</style>
                  </div>
                </div>
                <div className="flex justify-between mt-1 text-xs text-gray-500">
                  <span>Start: {activePackage.purchased_at ? new Date(activePackage.purchased_at).toLocaleDateString() : '--'}</span>
                  <span>End: {activePackage.expiry_date ? new Date(activePackage.expiry_date).toLocaleDateString() : '--'}</span>
                </div>
              </div>
            )}
          </div>
        )}
        {/* Card Footer - Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100 mt-auto">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">
              ID: {manager.id}
            </span>
            <div className="flex space-x-2">
              {/* Action buttons */}
              <Tooltip target={`.gift-btn-${manager.id}`} content="Gift Package" position="top" />
              <button
                className={`gift-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                style={{
                  backgroundColor: '#f3e8ff',
                  color: '#9333ea',
                  border: 'none'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#e9d5ff'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#f3e8ff'}
                onClick={(e) => {
                  e.stopPropagation();
                  openGiftDialog(manager);
                }}
              >
                <FaGift size={14} />
              </button>

              <Tooltip target={`.card-btn-${manager.id}`} content="Create Card" position="top" />
              <button
                className={`card-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                style={{
                  backgroundColor: '#dbeafe',
                  color: '#2563eb',
                  border: 'none'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#bfdbfe'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#dbeafe'}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedMember(manager);
                  dialogHandler("CreateCardToManagerForm", true);
                }}
              >
                <HiOutlineCreditCard size={14} />
              </button>

              <Tooltip target={`.edit-btn-${manager.id}`} content="Edit Manager" position="top" />
              <button
                className={`edit-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                style={{
                  backgroundColor: '#fef3c7',
                  color: '#d97706',
                  border: 'none'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#fde68a'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#fef3c7'}
                onClick={(e) => {
                  e.stopPropagation();
                  editMember(manager);
                }}
              >
                <FiEdit size={14} />
              </button>

              {manager.id.toString() !== localStorage.getItem("user_id") && (
                <>
                  <Tooltip target={`.delete-btn-${manager.id}`} content="Delete Manager" position="top" />
                  <button
                    className={`delete-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                    style={{
                      backgroundColor: '#fee2e2',
                      color: '#dc2626',
                      border: 'none'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#fecaca'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#fee2e2'}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteClick(manager);
                    }}
                  >
                    <TfiTrash size={14} />
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  ManagerCard.propTypes = {
    manager: PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      name: PropTypes.string,
      image: PropTypes.string,
      company_name: PropTypes.string,
      email: PropTypes.string,
      phone: PropTypes.string,
      department: PropTypes.string,
      packages: PropTypes.arrayOf(PropTypes.object),
    }).isRequired,
  };

  return (
    <>
      <Toast ref={toastRef} />
      <div className="w-full mt-8">
        {/* Header */}
        <Header
          searchQuery={searchQuery}
          handleSearchChange={handleSearchChange}
          isMobile={isMobile}
          createMember={createMember}
        />

        {/* Cards Grid */}
        {tableLoading ? (
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden animate-pulse">
                <div className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="px-6 pb-4">
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-300 rounded w-full"></div>
                    <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    <div className="flex space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={`grid gap-6 w-full min-h-[200px] ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
            {filteredData.map((manager) => (
              <ManagerCard key={manager.id} manager={manager} />
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {!searchQuery.trim() && totalRecords > 0 && (
          <div className="flex justify-center items-center gap-4 mt-8">
           
            <button
              onClick={goToPrevPage}
              disabled={lazyManagersParams.first === 0}
              className="px-4 py-2 bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            <span className="px-4 py-2 text-sm text-gray-600">
              Page {lazyManagersParams.page + 1} of {Math.ceil(totalRecords / lazyManagersParams.rows)}
            </span>
            <button
              onClick={goToNextPage}
              disabled={lazyManagersParams.first + lazyManagersParams.rows >= totalRecords}
              className="px-4 py-2 bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
             <span className="text-sm text-gray-600 mr-4">
              {`showing ${totalRecords === 0 ? 0 : lazyManagersParams.first + 1} to ${Math.min(lazyManagersParams.first + lazyManagersParams.rows, totalRecords)} of ${totalRecords} managers`}
            </span>
            <div className="flex items-center gap-2 ml-4">
              <span className="text-sm">Cards per page:</span>
              <Dropdown
                value={lazyManagersParams.rows}
                options={[5, 25, 50, 100].map(opt => ({ label: opt, value: opt }))}
                onChange={e => handleRowsPerPageChange({ target: { value: e.value } })}
                style={{ minWidth: 100, width: 130 }}
                placeholder="Choose"
              />
            </div>
          </div>
        )}

        {/* Empty State */}
        {allManagers.length === 0 && !tableLoading && (
          <div className="text-center py-12">
            <FaUser className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No managers found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery ? 'Try adjusting your search terms.' : 'Get started by adding a new manager.'}
            </p>
          </div>
        )}

        {/* Loading State */}
        {/* This block is now redundant as loading state is handled by the main conditional */}
        {/* {tableLoading && (
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden animate-pulse">
                <div className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="px-6 pb-4">
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-300 rounded w-full"></div>
                    <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    <div className="flex space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )} */}

        {/* Manager Detail Modal */}
        <Dialog
          visible={managerDetailModalVisible}
          style={{ width: isMobile ? "99vw" : "1020px" }}
          header="Manager Details"
          modal
          className="p-fluid"
          onHide={() => setManagerDetailModalVisible(false)}
        >
          {selectedManagerForModal && (
            <div className="space-y-6">
              {/* Manager Header */}
              <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                <img
                  src={selectedManagerForModal.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(selectedManagerForModal.name)}&background=00c3ac&color=fff&size=80`}
                  alt={selectedManagerForModal.name}
                  className="w-20 h-20 rounded-full object-cover border-4 border-white shadow-lg"
                />
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{selectedManagerForModal.name}</h2>
                  <p className="text-lg text-gray-600">{selectedManagerForModal.company_name || 'No Company'}</p>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white mt-2 ${
                    selectedManagerForModal.packages && selectedManagerForModal.packages.length > 0 && selectedManagerForModal.packages[selectedManagerForModal.packages.length - 1]?.status === "active"
                      ? "bg-green-500"
                      : selectedManagerForModal.packages && selectedManagerForModal.packages.length > 0
                        ? "bg-red-500"
                        : "bg-gray-500"
                  }`}>
                    {selectedManagerForModal.packages && selectedManagerForModal.packages.length > 0
                      ? selectedManagerForModal.packages[selectedManagerForModal.packages.length - 1]?.status
                      : "No Package"}
                  </span>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Contact Information</h3>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <FaEnvelope className="text-blue-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="text-gray-900">{selectedManagerForModal.email || 'Not provided'}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FaPhone className="text-green-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="text-gray-900">{selectedManagerForModal.phone || 'Not provided'}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FaBuilding className="text-purple-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Company</p>
                        <p className="text-gray-900">{selectedManagerForModal.company_name || 'Not provided'}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FaUser className="text-orange-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Department</p>
                        <p className="text-gray-900">{selectedManagerForModal.department || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Additional Information</h3>

                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500">Manager ID</p>
                      <p className="text-gray-900 font-mono">{selectedManagerForModal.id}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">User Type</p>
                      <p className="text-gray-900 capitalize">{selectedManagerForModal.user_type || 'Manager'}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Position</p>
                      <p className="text-gray-900">{selectedManagerForModal.position || 'Not specified'}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Created At</p>
                      <p className="text-gray-900">{new Date(selectedManagerForModal.created_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* --- Active Packages Section --- */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 flex items-center gap-2">
                  <HiOutlineCreditCard className="text-blue-500" /> Active Packages & Cards
                </h3>

                {loadingManagerPackages ? (
                  <div className="flex flex-col gap-4">
                    {[...Array(1)].map((_, i) => (
                      <div key={i} className="bg-white rounded-lg p-4 border border-gray-100 shadow animate-pulse">
                        <Skeleton width="100%" height="30px" className="mb-2" />
                        <Skeleton width="80%" height="20px" className="mb-2" />
                        <Skeleton width="60%" height="20px" />
                      </div>
                    ))}
                  </div>
                ) : managerPackagesError ? (
                  <div className="text-red-500 text-sm">{managerPackagesError}</div>
                ) : managerActivePackages && managerActivePackages.active_packages && managerActivePackages.active_packages.length > 0 ? (
                  <div className="flex flex-col gap-6">
                    {managerActivePackages.active_packages.map((pkg) => (
                      <div
                        key={pkg.id}
                        className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-5 border border-blue-100 shadow-md flex flex-col gap-4 relative overflow-hidden"
                      >
                        <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-6 mb-2 relative">
                        
                          {Number(pkg.total_price) === 0 ? (
                            <i
                              className="pi pi-gift"
                              style={{
                                position: 'absolute',
                                left: '50%',
                                top: '50%',
                                transform: 'translate(-50%, -50%)',
                                fontSize: '14rem',
                                color: 'rgba(139,92,246,0.13)',
                                zIndex: 1,
                                pointerEvents: 'none',
                                opacity: 1,
                              }}
                            />
                          ) : (
                            <BsBank2
                              style={{
                                position: 'absolute',
                                left: '50%',
                                top: '50%',
                                transform: 'translate(-50%, -50%)',
                                fontSize: '14rem',
                                color: 'rgba(37,99,235,0.13)',
                                zIndex: 1,
                                pointerEvents: 'none',
                                opacity: 1,
                              }}
                            />
                          )}
                          <div className="relative z-10">
                           
                            <div className="flex items-center gap-4 mb-4">
                              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100">
                                <HiOutlineCreditCard className="text-blue-500 text-2xl" />
                              </div>
                              <div className="flex-1">
                                <div className="text-2xl font-bold text-blue-900 mb-1">{pkg.name}</div>
                                <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${pkg.status === 'active' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-700'}`}>{pkg.status}</span>
                              </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                              <div className="flex items-center gap-2">
                                <FaHashtag className="text-gray-400" />
                                <span className="text-xs text-gray-500">Package ID</span>
                                <span className="font-mono text-base text-gray-800">{pkg.id}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaMoneyBillWave className="text-green-500" />
                                <span className="text-xs text-gray-500">Monthly Price</span>
                                <span className="font-mono text-base text-blue-700">${pkg.monthly_price}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaMoneyCheckAlt className="text-blue-500" />
                                <span className="text-xs text-gray-500">Yearly Price</span>
                                <span className="font-mono text-base text-blue-700">${pkg.yearly_price}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaPercent className="text-purple-500" />
                                <span className="text-xs text-gray-500">Yearly Discount</span>
                                <span className="text-base text-gray-800">{pkg.yearly_discount ?? '—'}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaRegClock className="text-yellow-500" />
                                <span className="text-xs text-gray-500">Subscription Duration</span>
                                <span className="text-base text-gray-800">{pkg.subscription_duration} month(s)</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <HiOutlineCreditCard className="text-blue-400" />
                                <span className="text-xs text-gray-500">Card Limit</span>
                                <span className="text-base text-gray-800">{pkg.card_limit}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaCalendarTimes className="text-red-400" />
                                <span className="text-xs text-gray-500">Expiry Date</span>
                                <span className="font-mono text-base text-gray-800">{pkg.expiry_date}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaCalendarCheck className="text-green-400" />
                                <span className="text-xs text-gray-500">Purchased At</span>
                                <span className="font-mono text-base text-gray-800">{pkg.purchased_at}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaDollarSign className="text-green-700" />
                                <span className="text-xs text-gray-500">Total Price</span>
                                <span className="font-mono text-base text-blue-700">${pkg.total_price}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaUserTie className="text-gray-700" />
                                <span className="text-xs text-gray-500">Purchased By Manager ID</span>
                                <span className="font-mono text-base text-gray-800">{pkg.purchased_by_manager_id}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaLayerGroup className="text-blue-700" />
                                <span className="text-xs text-gray-500">Cards Count</span>
                                <span className="font-bold text-base text-blue-900">{pkg.cards_count}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        {/* --- Cards Table --- */}
                        <div className="overflow-x-auto mt-2">
                         
                          {pkg.cards && pkg.cards.length > 0 && (
                            <div className="flex justify-end mb-2 gap-2">
                              {/* زر Excel */}
                              <div className="relative group">
                                <button
                                  className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:to-green-700 text-white text-xs font-semibold rounded-xl shadow-sm border border-green-200 transition-all duration-200 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-green-200"
                                  onClick={() => {
                                    const cards = pkg.cards.map(card => ({
                                      'Card ID': card.id,
                                      'Name': card.name,
                                      'Number': card.number,
                                      'Card Type': card.card_type?.name,
                                      'Created At': card.created_at,
                                      'Updated At': card.updated_at,
                                      'Package Name': pkg.name,
                                      'Package ID': pkg.id,
                                    }));
                                    if (cards.length === 0) return;
                                    const ws = XLSX.utils.json_to_sheet(cards);
                                    const wb = XLSX.utils.book_new();
                                    XLSX.utils.book_append_sheet(wb, ws, 'Cards');
                                    XLSX.writeFile(wb, `manager_${selectedManagerForModal?.id || ''}_package_${pkg.id}_cards.xlsx`);
                                  }}
                                  type="button"
                                  style={{ minWidth: 0 }}
                                >
                                  <FaFileExcel size={16} className="text-white drop-shadow" />
                                  <span>Excel</span>
                                </button>
                                {/* Tooltip */}
                                <div className="absolute right-0 top-full mt-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 z-50">
                                  <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-1 shadow-lg whitespace-nowrap">
                                    Expot Card To Excel
                                  </div>
                                </div>
                              </div>
                              {/* زر Print PDF */}
                              <div className="relative group">
<button
  className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-gray-400 via-gray-500 to-gray-600 hover:from-gray-500 hover:to-gray-700 text-white text-xs font-semibold rounded-xl shadow-sm border border-gray-200 transition-all duration-200 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-gray-200"
  onClick={() => {
    const table = document.getElementById(`cards-table-${pkg.id}`);
    if (!table) return;
    const printWindow = window.open('', '', 'width=900,height=700');
    printWindow.document.write('<html><head><title>Print Cards</title>');
    printWindow.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">');
    printWindow.document.write('<style>@media print {@page {margin: 1cm;} html,body{margin:0!important;padding:0!important;} .print-hide-details{display:none!important;} }</style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write('<h2 style="font-size:20px;font-weight:bold;margin-bottom:10px;">Cards Table</h2>');
    printWindow.document.write(table.outerHTML);
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => { printWindow.print(); printWindow.close(); }, 500);
  }}
  type="button"
  style={{ minWidth: 0 }}
>
  <FaPrint size={16} className="text-white drop-shadow" />
  <span>Print PDF</span>
</button>
                                {/* Tooltip */}
                                <div className="absolute right-0 top-full mt-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 z-50">
                                  <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-1 shadow-lg whitespace-nowrap">
                                    Print Table
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                          <table id={`cards-table-${pkg.id}`} className="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden shadow bg-white">
                            <thead className="bg-blue-50">
                              <tr>
                                <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">ID</th>
                                <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Name</th>
                                <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Number</th>
                                <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Card Type</th>
                                <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Created At</th>
                                <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 uppercase tracking-wider">Updated At</th>
                                <th className="px-3 py-2 text-center text-xs font-semibold text-blue-900 uppercase tracking-wider print-hide-details">Details</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-100">
                              {pkg.cards.map(card => (
                                <tr key={card.id} className="hover:bg-blue-50 transition">
                                  <td className="px-3 py-2 font-mono text-gray-700 text-center">{card.id}</td>
                                  <td className="px-3 py-2 font-medium text-gray-900 truncate max-w-[120px]" title={card.name}>{card.name}</td>
                                  <td className="px-3 py-2 font-mono text-gray-700 truncate max-w-[120px]" title={card.number}>{card.number}</td>
                                  <td className="px-3 py-2 text-gray-700 text-center">{card.card_type?.name}</td>
                                  <td className="px-3 py-2 text-gray-700 text-center">{card.created_at ? new Date(card.created_at).toLocaleDateString() : ''}</td>
                                  <td className="px-3 py-2 text-gray-700 text-center">{card.updated_at ? new Date(card.updated_at).toLocaleDateString() : ''}</td>
                                  <td className="px-3 py-2 text-center print-hide-details">
                                    <div className="flex items-center justify-center gap-2">
                                      <button
                                        className={`details-btn-${card.id} px-2 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition text-xs font-semibold shadow-sm focus:outline-none`}
                                        onClick={() => {
                                          setSelectedCardForDetails(card);
                                          setCardDetailsModalVisible(true);
                                        }}
                                        title="View Card Details"
                                        style={{ minWidth: 36, minHeight: 36, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                                      >
                                        View
                                      </button>
                                      {/* Unassign Button - أيقونة فقط مع Tooltip */}
                                      <div className="relative group">
                                        <button
                                          className={`unassign-btn-${card.id} p-2 bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white rounded-full shadow-md hover:from-red-600 hover:to-red-800 focus:outline-none transition-all duration-150 flex items-center justify-center`}
                                          style={{ minWidth: 36, minHeight: 36 }}
                                          onClick={async () => {
                                            if (!selectedManagerForModal?.id) return;
                                            try {
                                              const token = localStorage.getItem('token');
                                              await axios.post(`${import.meta.env.VITE_BACKEND_URL}/packages/unassign-card`, {
                                                user_id: selectedManagerForModal.id,
                                                card_number: card.number
                                              }, {
                                                headers: { Authorization: `Bearer ${token}` }
                                              });
                                              // Remove card from UI
                                              setManagerActivePackages(prev => {
                                                if (!prev || !prev.active_packages) return prev;
                                                const updatedPackages = prev.active_packages.map(pkg0 => {
                                                  if (pkg0.id === pkg.id) {
                                                    return {
                                                      ...pkg0,
                                                      cards: (pkg0.cards || []).filter(c => c.id !== card.id)
                                                    };
                                                  }
                                                  return pkg0;
                                                });
                                                return { ...prev, active_packages: updatedPackages };
                                              });
                                              setLeftCards(prev => prev.some(c => c.id === card.id) ? prev : [...prev, card]);
                                              toastRef.current?.show({
                                                severity: 'success',
                                                summary: 'Success',
                                                detail: 'Card unassigned successfully!',
                                                life: 3000,
                                              });
                                            } catch (error) {
                                              toastRef.current?.show({
                                                severity: 'error',
                                                summary: 'Error',
                                                detail: error.response?.data?.message || 'Failed to unassign card',
                                                life: 3000,
                                              });
                                            }
                                          }}
                                          tabIndex={0}
                                          title="Unassign this card from manager"
                                        >
                                          <TfiTrash className="text-white text-base" />
                                        </button>
                                        {/* Tooltip */}
                                        <div className="absolute left-1/2 -translate-x-1/2 top-full mt-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 z-50 whitespace-nowrap px-3 py-1 bg-gray-900 text-white text-xs rounded-lg shadow-lg">
                                          Unassign Card
                                        </div>
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                              {/* 3. Insert the new section inside the Manager Details modal, after the existing content, before the modal footer */}
      {managerDetailModalVisible && (
        <div className="mt-8">
          <h3 className="text-lg font-bold mb-4 text-center">Assign Cards to Manager</h3>
          <CardsDnDSection
            leftCards={leftCards}
            setLeftCards={setLeftCards}
            rightCards={rightCards}
            setRightCards={setRightCards}
            cardsLoading={cardsLoading}
            assignLoading={assignLoading}
            handleAssignCards={handleAssignCards}
          />
        </div>
      )}
                      </div>
                    ))}
                    
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm">No active packages found for this manager.</div>
                )}

                
              </div>

              {/* Package Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Package History</h3>
                {loadingManagerPackageHistory ? (
                  <div className="flex flex-col gap-4">
                    {[...Array(2)].map((_, i) => (
                      <div key={i} className="bg-white rounded-lg p-4 border border-gray-100 shadow animate-pulse">
                        <Skeleton width="100%" height="30px" className="mb-2" />
                        <Skeleton width="80%" height="20px" className="mb-2" />
                        <Skeleton width="60%" height="20px" />
                      </div>
                    ))}
                  </div>
                ) : managerPackageHistoryError ? (
                  <div className="text-red-500 text-sm">{managerPackageHistoryError}</div>
                ) : managerPackageHistory && managerPackageHistory.length > 0 ? (
                  <div className="grid gap-6">
                    {[...managerPackageHistory]
                      .sort((a, b) => new Date(b.purchased_at) - new Date(a.purchased_at))
                      .map((pkg, index) => (
                        <div
                          key={index}
                          className={`p-4 rounded-lg border border-gray-200 transition-all duration-300 cursor-pointer ${
                            pkg.status === 'active'
                              ? 'bg-green-50'
                              : 'bg-red-50'
                          }`}
                          style={{ boxShadow: '0 1px 4px 0 rgba(0,0,0,0.04)' }}
                          onMouseEnter={e => {
                            e.currentTarget.style.boxShadow = pkg.status === 'active'
                              ? '0 8px 32px 0 rgba(34,197,94,0.22), 0 2px 8px 0 rgba(34,197,94,0.10)'
                              : '0 8px 32px 0 rgba(239,68,68,0.18), 0 2px 8px 0 rgba(239,68,68,0.10)';
                          }}
                          onMouseLeave={e => e.currentTarget.style.boxShadow = '0 1px 4px 0 rgba(0,0,0,0.04)'}
                        >
                        <div className="flex justify-between items-start">
                          <div>
                              <p className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                                <FaBoxOpen className="text-blue-500" />
                                {pkg.package_name || `Package #${index + 1}`}
                              </p>
                              <p className="text-sm text-gray-600 mb-2 flex items-center gap-2">
                                {pkg.status === 'active' ? (
                                  <FaCheckCircle className="text-green-500" />
                                ) : (
                                  <FaTimesCircle className="text-red-500" />
                                )}
                                Status:
                              <span
                                style={{
                                    marginLeft: "0.25rem",
                                    padding: "0.15rem 0.5rem",
                                    borderRadius: "0.25rem",
                                    fontSize: "0.75rem",
                                    fontWeight: 500,
                                    color: "#fff",
                                    backgroundColor: pkg.status === "active" ? "#22C55E" : "#EF4444",
                                }}
                              >
                                {pkg.status}
                              </span>
                              </p>
                              <p className="text-sm text-gray-600 mb-2 flex items-center gap-2">
                                <FaDollarSign className="text-green-600" />
                                Total Price: <span className="text-gray-900 font-semibold">${pkg.total_price}</span>
                              </p>
                              <p className="text-sm text-gray-600 mb-2 flex items-center gap-2">
                                <FaLayerGroup className="text-blue-700" />
                                Allowed Cards Count: <span className="text-gray-900 font-semibold">{pkg.card_limit}</span>
                            </p>
                          </div>
                          <div className="text-right">
                              <p className="text-sm text-gray-500 mb-2 flex items-center gap-2 justify-end">
                                <FaCalendarCheck className="text-green-500" />
                                Purchased
                              </p>
                              <p className="text-sm text-gray-900 mb-2 flex items-center gap-2 justify-end">
                                {pkg.purchased_at ? new Date(pkg.purchased_at).toLocaleDateString() : ''}
                              </p>
                              <p className="text-sm text-gray-500 mb-2 flex items-center gap-2 justify-end">
                                <FaCalendarTimes className="text-red-400" />
                                Expiry Date
                              </p>
                              <p className="text-sm text-gray-900 mb-2 flex items-center gap-2 justify-end">
                                {pkg.expiry_date ? new Date(pkg.expiry_date).toLocaleDateString() : ''}
                              </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm">No package history found for this manager.</div>
                )}
                </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                <button
                  style={{
                    backgroundColor: '#9333ea',
                    color: 'white',
                    border: 'none',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    transition: 'background-color 0.2s',
                    fontWeight: '500'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#7c3aed'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#9333ea'}
                  onClick={() => {
                    setManagerDetailModalVisible(false);
                    openGiftDialog(selectedManagerForModal);
                  }}
                >
                  <FaGift />
                  <span>Gift Package</span>
                </button>

                <button
                  style={{
                    backgroundColor: '#2563eb',
                    color: 'white',
                    border: 'none',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    transition: 'background-color 0.2s',
                    fontWeight: '500'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#1d4ed8'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#2563eb'}
                  onClick={() => {
                    setManagerDetailModalVisible(false);
                    setSelectedMember(selectedManagerForModal);
                    dialogHandler("CreateCardToManagerForm", true);
                  }}
                >
                  <HiOutlineCreditCard />
                  <span>Create Card</span>
                </button>

                <button
                  style={{
                    backgroundColor: '#d97706',
                    color: 'white',
                    border: 'none',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    transition: 'background-color 0.2s',
                    fontWeight: '500'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#b45309'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#d97706'}
                  onClick={() => {
                    setManagerDetailModalVisible(false);
                    editMember(selectedManagerForModal);
                  }}
                >
                  <FiEdit />
                  <span>Edit Manager</span>
                </button>

                {selectedManagerForModal.id.toString() !== localStorage.getItem("user_id") && (
                  <button
                    style={{
                      backgroundColor: '#dc2626',
                      color: 'white',
                      border: 'none',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'background-color 0.2s',
                      fontWeight: '500'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#b91c1c'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#dc2626'}
                    onClick={() => {
                      setManagerDetailModalVisible(false);
                      handleDeleteClick(selectedManagerForModal);
                    }}
                  >
                    <TfiTrash />
                    <span>Delete Manager</span>
                  </button>
                )}
              </div>
            </div>
          )}
        </Dialog>

        <Dialog
          visible={giftDialogVisible}
          style={{ width: "650px" }} // Increased from 500px to 650px
          header="Assign Package to User"
          modal
          className="p-fluid"
          footer={
            <div className="flex justify-between w-full pt-3"> {/* Changed from justify-end to justify-between */}
              <Button
                label="Cancel"
                icon="pi pi-times"
                onClick={() => setGiftDialogVisible(false)}
                className="p-button-danger p-button-rounded"
              />
              <Button
                label="Assign Package"
                icon="pi pi-gift"
                onClick={handleGiftSubmit}
                loading={giftLoading}
                className="p-button-success p-button-rounded"
              />
            </div>
          }
          onHide={() => setGiftDialogVisible(false)}
        >
          <div className="gift-form p-4">
            {/* Payment method icon animation */}
            <div className="flex justify-center mb-4">
              <motion.div
                key={animationKey}
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, type: "spring", bounce: 0.4 }}
                className="p-6 rounded-full bg-gray-100" // Increased padding
              >
                {formData.payment_method === "gift" ? (
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 10, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <FaGift size={80} className="text-purple-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                ) : (
                  <motion.div
                    animate={{ y: [0, -5, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <BsBank2 size={80} className="text-blue-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                )}
              </motion.div>
            </div>

            <div className="field mb-4">
              <label htmlFor="package" className="block text-sm font-medium mb-2">
                Select Package
              </label>
              <Dropdown
                id="package"
                value={formData.package_id}
                options={availablePackages.map(pkg => ({
                  label: `${pkg.name} (${pkg.card_limit} cards)`,
                  value: pkg.id
                }))}
                onChange={(e) => {
                  const selectedPkg = availablePackages.find(p => p.id === e.value);
                  setFormData({
                    ...formData,
                    package_id: e.value,
                    total_price: formData.payment_method === "bank_transfer" ?
                      (formData.duration === 12 ? selectedPkg?.yearly_price : selectedPkg?.monthly_price) : 0
                  });
                  setSelectedPackage(selectedPkg);
                }}
                placeholder="Select a package"
                className="w-full"
                disabled={giftLoading}
                required
                optionLabel="label"
              />
              {selectedPackage && (
                <div className="mt-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Monthly Price:</span>
                    <span>${selectedPackage.monthly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Price:</span>
                    <span>${selectedPackage.yearly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Card Types:</span>
                    <span>{selectedPackage.card_type_names.join(', ')}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="field mb-4">
              <label htmlFor="duration" className="block text-sm font-medium mb-2">
                Subscription Duration
              </label>
              <Dropdown
                id="duration"
                value={formData.duration}
                options={[
                  { label: "Monthly (1 month)", value: 1 },
                  { label: "Quarterly (3 months)", value: 3 },
                  { label: "Semi-Annual (6 months)", value: 6 },
                  { label: "Annual (12 months)", value: 12 }
                ]}
                onChange={(e) => {
                  // Update duration
                  setFormData(prev => ({ ...prev, duration: e.value }));

                  // Suggest a price if package is selected and payment method is bank transfer
                  if (selectedPackage && formData.payment_method === "bank_transfer") {
                    let suggestedPrice;

                    if (e.value === 12) {
                      suggestedPrice = selectedPackage.yearly_price;
                    } else if (e.value === 6) {
                      suggestedPrice = (selectedPackage.monthly_price * 6) * 0.9; // 10% discount
                    } else if (e.value === 3) {
                      suggestedPrice = (selectedPackage.monthly_price * 3) * 0.95; // 5% discount
                    } else {
                      suggestedPrice = selectedPackage.monthly_price;
                    }

                    // Suggest the price but don't force it - user can still edit manually
                    setFormData(prev => ({
                      ...prev,
                      duration: e.value,
                      total_price: suggestedPrice
                    }));
                  }
                }}
                placeholder="Select duration"
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            <div className="field mb-4">
              <label htmlFor="payment_method" className="block text-sm font-medium mb-2">
                Payment Method
              </label>
              <Dropdown
                id="payment_method"
                value={formData.payment_method}
                options={[
                  { label: "Gift (Free)", value: "gift" },
                  { label: "Bank Transfer", value: "bank_transfer" }
                ]}
                onChange={(e) => {
                  setFormData({ ...formData, payment_method: e.value });
                  // Trigger animation when payment method changes
                  setAnimationKey(prev => prev + 1);
                }}
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            {formData.payment_method === "bank_transfer" && (
              <div className="field mb-4">
                <label htmlFor="total_price" className="block text-sm font-medium mb-2">
                  Total Price
                </label>
                <InputNumber
                  id="total_price"
                  value={formData.total_price}
                  onValueChange={(e) => setFormData({ ...formData, total_price: e.value })}
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  className="w-full"
                  disabled={giftLoading}
                  required
                />
                <small className="text-blue-600 mt-1 block">
                  You can adjust this price manually if needed.
                </small>
              </div>
            )}

            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <i className="pi pi-info-circle mr-2"></i>
                {formData.payment_method === "gift"
                  ? "This will provide a free package to the user."
                  : "This will assign a paid package via bank transfer."}
              </p>
            </div>
          </div>
        </Dialog>

        {openDialog?.addMember && (
          <AddMemberDialog data={selectedMember} actionType={actionType} />
        )}
        {openDialog?.createGroup && (
          <AssignGroupDialog data={[]} />
        )}
        {openDialog?.updateGroup && <AssignGroupDialog />}

        {openDialog?.CreateCardToManagerForm &&
          selectedMember &&
          selectedMember.packages && (
            <CreateCardToManagerForm
              userId={selectedMember.id}
              packages={selectedMember.packages.filter(
                (pkg) => pkg.status === "active"
              )}
            />
          )}
      </div>
      {/* ConfirmDialog for delete confirmation */}
      <ConfirmDialog group="headless"
        content={(options) => (
          <div className="flex flex-col items-center p-5">
            <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
            <span className="text-xl font-bold mb-2">{options.message}</span>
            <div className="flex gap-3">
              <button className="p-button p-component" onClick={options.accept}>
                Yes
              </button>
              <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                No
              </button>
            </div>
          </div>
        )}
      />
      {/* Card Details Modal */}
      <Dialog
        visible={cardDetailsModalVisible}
        style={{ width: '450px' }}
        header="Card Details"
        modal
        className="p-fluid"
        onHide={() => setCardDetailsModalVisible(false)}
      >
        {selectedCardForDetails && (
          <div className="space-y-3">
            <div className="flex flex-col gap-2">
              <div><span className="font-semibold">ID:</span> {selectedCardForDetails.id}</div>
              <div><span className="font-semibold">Name:</span> {selectedCardForDetails.name}</div>
              <div><span className="font-semibold">Number:</span> {selectedCardForDetails.number}</div>
              <div><span className="font-semibold">Created At:</span> {selectedCardForDetails.created_at}</div>
              <div><span className="font-semibold">Updated At:</span> {selectedCardForDetails.updated_at}</div>
              {selectedCardForDetails.card_type && (
                <div className="bg-blue-50 rounded p-2 mt-2">
                  <div className="font-semibold text-blue-800 mb-1">Card Type Details:</div>
                  <div><span className="font-semibold">Name:</span> {selectedCardForDetails.card_type.name}</div>
                  <div><span className="font-semibold">Type Connection:</span> {selectedCardForDetails.card_type.type_of_connection}</div>
                  <div><span className="font-semibold">Colors:</span> {selectedCardForDetails.card_type.number_of_colors}</div>
                  {selectedCardForDetails.card_type.setting && (
                    <div className="mt-1">
                      <span className="font-semibold">Settings:</span>
                      <ul className="ml-4 list-disc">
                        {Object.entries(selectedCardForDetails.card_type.setting).map(([key, value]) => (
                          <li key={key} className="text-xs text-gray-600">{key}: {value}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <div><span className="font-semibold">Created At:</span> {selectedCardForDetails.card_type.created_at}</div>
                  <div><span className="font-semibold">Updated At:</span> {selectedCardForDetails.card_type.updated_at}</div>
                </div>
              )}
              {/* Show any other fields dynamically */}
              {Object.entries(selectedCardForDetails).map(([key, value]) => (
                !['id','name','number','card_type','created_at','updated_at'].includes(key) && value ? (
                  <div key={key}><span className="font-semibold">{key}:</span> {typeof value === 'object' ? JSON.stringify(value) : value}</div>
                ) : null
              ))}
            </div>
          </div>
        )}
      </Dialog>

    </>
  );
}

export default ManagersDataTable;

// tailwind animation
<style>{`
@keyframes fade-in-up {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}
.animate-fade-in-up {
  animation: fade-in-up 0.25s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
@media print {
  @page { margin: 1cm; }
  html, body { margin: 0 !important; padding: 0 !important; }
  .print-hide-details { display: none !important; }
}
`}</style>


function DraggableCard({card, id, from, isOverlay, justDropped, selected}) {
  const {attributes, listeners, setNodeRef, transform, isDragging} = useSortable({id});
  const style = {
    minHeight: 48,
    width: '100%',
    maxWidth: 420,
    boxSizing: 'border-box',
    transform: isOverlay
      ? `${CSS.Transform.toString(transform)} rotateY(8deg) rotateX(2deg) scale(1.09)`
      : CSS.Transform.toString(transform),
    transition: 'box-shadow 0.12s, transform 0.12s',
    opacity: isOverlay ? 1 : isDragging ? 0.85 : 1,
    background: from === 'left' ? '#f9fafb' : '#f0fdf4',
    marginBottom: 8,
    padding: 12,
    borderRadius: 10,
    // فقط عيّن border مرة واحدة بناءً على selected
    border: selected ? '2px solid #a78bfa' : (from === 'left' ? '1px solid #ddd' : '1px solid #22c55e'),
    cursor: 'move',
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    zIndex: isOverlay ? 99999 : isDragging ? 9999 : 'auto',
    // فقط عيّن boxShadow مرة واحدة بناءً على selected
    boxShadow: selected ? '0 0 0 2px #a78bfa' : (isOverlay
      ? '0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55, 0 4px 16px 0 rgba(168,139,250,0.18), 0 1px 4px 0 rgba(168,139,250,0.10), inset 0 1px 4px #a78bfa22'
      : isDragging
        ? '0 0 0 2px #a78bfa, 0 4px 16px 0 rgba(168,139,250,0.10)'
        : 'none'),
    willChange: 'transform, box-shadow, opacity',
    scale: isOverlay || isDragging ? 1.09 : 1,
    position: isOverlay || isDragging ? 'relative' : 'static',
    animation: isOverlay
      ? 'bounce-drag 0.22s cubic-bezier(.68,-0.55,.27,1.55) 1, gradient-glow 1.2s ease-in-out infinite, vibrate-purple 0.22s linear infinite'
      : justDropped
        ? 'flash-card 0.7s linear 1'
        : isDragging
          ? 'vibrate-purple 0.25s linear infinite'
          : 'none',
    pointerEvents: isOverlay ? 'none' : 'auto',
  };
  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <span className="font-mono text-sm text-blue-900">{card.number}</span>
      <span className="text-xs text-gray-500">{card.name}</span>
      <span className="text-xs text-gray-400">({card.card_type?.name || 'Type'})</span>
      {/* ... styles ... */}
      <style>{`
        @keyframes vibrate-purple {
          0% { transform: translate(0, 0) scale(1.09) rotateY(8deg) rotateX(2deg); }
          20% { transform: translate(-1.5px, 1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          40% { transform: translate(-1.5px, -1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          60% { transform: translate(1.5px, 1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          80% { transform: translate(1.5px, -1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          100% { transform: translate(0, 0) scale(1.09) rotateY(8deg) rotateX(2deg); }
        }
        @keyframes gradient-glow {
          0% { box-shadow: 0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55; }
          50% { box-shadow: 0 0 0 8px #c4b5fd, 0 0 24px 8px #a78bfa99; }
          100% { box-shadow: 0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55; }
        }
        @keyframes bounce-drag {
          0% { transform: scale(1) rotateY(8deg) rotateX(2deg); }
          30% { transform: scale(1.13) rotateY(8deg) rotateX(2deg); }
          60% { transform: scale(1.09) rotateY(8deg) rotateX(2deg); }
          100% { transform: scale(1.09) rotateY(8deg) rotateX(2deg); }
        }
        @keyframes flash-card {
          0% { box-shadow: 0 0 0 0 #a78bfa, 0 0 16px 4px #a78bfa55; }
          30% { box-shadow: 0 0 0 6px #fbbf24, 0 0 24px 8px #a78bfa99; }
          60% { box-shadow: 0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55; }
          100% { box-shadow: 0 0 0 0 #a78bfa, 0 0 16px 4px #a78bfa55; }
        }
      `}</style>
    </div>
  );
}
DraggableCard.propTypes = {
  card: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    number: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string,
    card_type: PropTypes.shape({
      name: PropTypes.string
    })
  }).isRequired,
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  from: PropTypes.string.isRequired,
  isOverlay: PropTypes.bool,
  justDropped: PropTypes.bool,
  selected: PropTypes.bool
};

function DroppableContainer({id, children, style, justDropped}) {
  const {setNodeRef} = useDroppable({id});
  return (
    <div
      ref={setNodeRef}
      id={id}
      style={{
        ...style,
        transition: 'box-shadow 0.12s, border-color 0.12s, background 0.12s',
        willChange: 'box-shadow, border-color, background',
        zIndex: id === 'right-list' ? 100 : 'auto',
        pointerEvents: 'auto',
      }}
    >
      {children}
      {id === 'right-list' && justDropped && (
        <span style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: 70,
          height: 70,
          background: 'radial-gradient(circle, #a78bfa55 0%, #a78bfa00 80%)',
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none',
          animation: 'ripple-effect 0.5s cubic-bezier(.4,0,.2,1) 1',
          zIndex: 1000
        }} />
      )}
      <style>{`
        @keyframes ripple-effect {
          0% { opacity: 0.7; transform: translate(-50%, -50%) scale(0.7); }
          60% { opacity: 0.4; transform: translate(-50%, -50%) scale(1.1); }
          100% { opacity: 0; transform: translate(-50%, -50%) scale(1.5); }
        }
      `}</style>
    </div>
  );
}

DroppableContainer.propTypes = {
  id: PropTypes.string.isRequired,
  children: PropTypes.node,
  style: PropTypes.object,
  justDropped: PropTypes.bool
};

function groupCardsByType(cards) {
  return cards.reduce((groups, card) => {
    const type = card.card_type?.name || 'Other';
    if (!groups[type]) groups[type] = [];
    groups[type].push(card);
    return groups;
  }, {});
}

function CardsDnDSection({leftCards, setLeftCards, rightCards, setRightCards, cardsLoading, assignLoading, handleAssignCards}) {
  const sensors = useSensors(useSensor(PointerSensor));
  const [isOverRight, setIsOverRight] = useState(false);
  const [justDropped, setJustDropped] = useState(false);
  const [justDroppedCardId, setJustDroppedCardId] = useState(null);
  const [activeCard, setActiveCard] = useState(null);
  const popAudioRef = useRef();
  const [audioActivated, setAudioActivated] = useState(false);
  // نقل حالة التحديد هنا
  const [selectedCardIds, setSelectedCardIds] = useState([]);
  const [activeCardIds, setActiveCardIds] = useState([]);

  const handleActivateAudio = () => {
    if (popAudioRef.current) {
      popAudioRef.current.volume = 0.01;
      popAudioRef.current.play().catch(()=>{});
      setAudioActivated(true);
    }
  };

  const handleDragStart = (event) => {
    if (!audioActivated && popAudioRef.current) {
      popAudioRef.current.volume = 0.01;
      popAudioRef.current.play().catch(()=>{});
      setAudioActivated(true);
    }
    const { active } = event;
    if (selectedCardIds.includes(active.id)) {
      setActiveCardIds(selectedCardIds);
    } else {
      setActiveCardIds([active.id]);
    }
    const card = leftCards.find(c => c.id === active.id) || rightCards.find(c => c.id === active.id);
    setActiveCard(card);
  };

  const handleDragEnd = (event) => {
    setIsOverRight(false);
    setActiveCard(null);
    const {active, over} = event;
    if (!over) {
      setActiveCardIds([]);
      return;
    }
    if (active.id === over.id) {
      setActiveCardIds([]);
      return;
    }
    let targetList = null;
    if (over.id === 'left-list') {
      targetList = 'left';
    } else if (over.id === 'right-list') {
      targetList = 'right';
    } else if (rightCards.some(c => c.id === over.id)) {
      targetList = 'right';
    } else if (leftCards.some(c => c.id === over.id)) {
      targetList = 'left';
    }
    if (!targetList) {
      setActiveCardIds([]);
      return;
    }
    // نقل جماعي
    if (activeCardIds.length > 1) {
      if (targetList === 'left') {
        const moved = rightCards.filter(c => activeCardIds.includes(c.id));
        setRightCards(prev => prev.filter(c => !activeCardIds.includes(c.id)));
        setLeftCards(prev => [...prev, ...moved]);
      } else if (targetList === 'right') {
        const moved = leftCards.filter(c => activeCardIds.includes(c.id));
        setLeftCards(prev => prev.filter(c => !activeCardIds.includes(c.id)));
        setRightCards(prev => [...prev, ...moved]);
        setJustDropped(true);
        setJustDroppedCardId(activeCardIds[0]);
        if (popAudioRef.current) {
          popAudioRef.current.volume = 1;
          popAudioRef.current.currentTime = 0;
          popAudioRef.current.play().catch(()=>{});
        }
        setTimeout(() => {
          setJustDropped(false);
          setJustDroppedCardId(null);
        }, 900);
      }
      setSelectedCardIds([]);
      setActiveCardIds([]);
      return;
    }
    // نقل فردي
    if (targetList === 'left' && rightCards.find(c => c.id === active.id)) {
      const card = rightCards.find(c => c.id === active.id);
      setRightCards(prev => prev.filter(c => c.id !== active.id));
      setLeftCards(prev => [...prev, card]);
    }
    else if (targetList === 'right' && leftCards.find(c => c.id === active.id)) {
      const card = leftCards.find(c => c.id === active.id);
      setLeftCards(prev => prev.filter(c => c.id !== active.id));
      setRightCards(prev => [...prev, card]);
      setJustDropped(true);
      setJustDroppedCardId(card.id);
      if (popAudioRef.current) {
        popAudioRef.current.volume = 1;
        popAudioRef.current.currentTime = 0;
        popAudioRef.current.play().catch(()=>{});
      }
      setTimeout(() => {
        setJustDropped(false);
        setJustDroppedCardId(null);
      }, 900);
    }
    setActiveCardIds([]);
    setSelectedCardIds([]);
  };

  const handleDragOver = (event) => {
    if (event.over && event.over.id === 'right-list') {
      setIsOverRight(true);
    } else {
      setIsOverRight(false);
    }
  };
  const groupedLeftCards = groupCardsByType(leftCards);
  useEffect(() => {
    if (justDropped && popAudioRef.current) {
      popAudioRef.current.currentTime = 0;
      popAudioRef.current.play().catch(()=>{});
    }
  }, [justDropped]);
  // --- Multi Select UI ---
  const allLeftCardIds = leftCards.map(card => card.id);
  const allSelected = allLeftCardIds.length > 0 && allLeftCardIds.every(id => selectedCardIds.includes(id));
  const someSelected = selectedCardIds.length > 0 && !allSelected;
  // معالجة indeterminate
  const selectAllRef = useRef();
  useEffect(() => {
    if (selectAllRef.current) {
      selectAllRef.current.indeterminate = someSelected;
    }
  }, [someSelected]);
  return (
    <>
      <audio ref={popAudioRef} src="https://cdn.pixabay.com/audio/2022/03/15/audio_115b9b7bfa.mp3" preload="auto" />
     
     
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
      >
        <div style={{display: 'flex', gap: 32}}>
          <SortableContext id="left-list" items={leftCards.map(c => c.id)} strategy={verticalListSortingStrategy}>
            <DroppableContainer id="left-list" style={{
              minHeight: 120,
              flex: 1,
              background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',
              borderRadius: 18,
              border: '2px solid #60a5fa',
              padding: 18,
              overflowY: 'auto',
              maxHeight: 420,
              boxShadow: '0 4px 24px 0 rgba(96,165,250,0.08)',
              transition: 'box-shadow 0.3s, border-color 0.3s',
            }} justDropped={justDropped}>
              <div className="flex items-center gap-3 mb-4">
                <svg width="28" height="28" viewBox="0 0 32 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="1" y="2" width="30" height="18" rx="4" fill="url(#card-gradient2)" stroke="#2563eb" strokeWidth="1.5"/>
                  <rect x="1" y="7" width="30" height="3" fill="#e0e7ff"/>
                  <circle cx="7" cy="16" r="2" fill="#fbbf24"/>
                  <rect x="12" y="15" width="10" height="2" rx="1" fill="#fff" opacity="0.7"/>
                  <defs>
                    <linearGradient id="card-gradient2" x1="1" y1="2" x2="31" y2="20" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#60a5fa"/>
                      <stop offset="1" stopColor="#a5b4fc"/>
                    </linearGradient>
                  </defs>
                </svg>
                <h4 className="font-bold text-blue-700 text-lg">Available Cards</h4>
                <div className="flex items-center gap-2 ml-2">
                  <input
                    type="checkbox"
                    ref={selectAllRef}
                    checked={allSelected}
                    onChange={e => {
                      if (e.target.checked) {
                        setSelectedCardIds(allLeftCardIds);
                      } else {
                        setSelectedCardIds([]);
                      }
                    }}
                    className="accent-blue-700 w-4 h-4 ml-12"
                  />
                  <span className="text-sm font-medium text-blue-700 ">Select All</span>
                  
                </div>
              </div>
              {cardsLoading && <div className="text-center text-gray-400">Loading...</div>}
              {!cardsLoading && Object.keys(groupedLeftCards).length === 0 && (
                <div className="text-center text-gray-400">No cards available</div>
              )}
              {Object.entries(groupedLeftCards).map(([type, cards]) => (
                <div key={type} className="mb-4 rounded-xl border p-3" style={{ background: '#ede9fe', borderColor: '#a78bfa' }}>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="inline-flex items-center justify-center w-8 h-8 mr-1">
                      <svg width="32" height="22" viewBox="0 0 32 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="1" y="2" width="30" height="18" rx="4" fill="url(#card-gradient)" stroke="#a78bfa" strokeWidth="1.5"/>
                        <rect x="1" y="7" width="30" height="3" fill="#e0e7ff"/>
                        <circle cx="7" cy="16" r="2" fill="#fbbf24"/>
                        <rect x="12" y="15" width="10" height="2" rx="1" fill="#fff" opacity="0.7"/>
                        <defs>
                          <linearGradient id="card-gradient" x1="1" y1="2" x2="31" y2="20" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#a78bfa"/>
                            <stop offset="1" stopColor="#ede9fe"/>
                          </linearGradient>
                        </defs>
                      </svg>
                    </span>
                    <span className="font-bold text-purple-700">{type}</span>
                    <span className="ml-2 text-xs text-purple-700 bg-purple-100 rounded-full px-2 py-0.5">{cards.length} cards</span>
                  </div>
                  {cards.map(card => (
                    <div key={card.id} className="flex items-center gap-2 mb-1">
                      <input
                        type="checkbox"
                        checked={selectedCardIds.includes(card.id)}
                        onChange={() => setSelectedCardIds(ids => ids.includes(card.id) ? ids.filter(i => i !== card.id) : [...ids, card.id])}
                        className="accent-purple-500"
                      />
                      <DraggableCard
                        card={card}
                        id={card.id}
                        from="left"
                        isOverlay={false}
                        justDropped={false}
                        selected={selectedCardIds.includes(card.id)}
                      />
                    </div>
                  ))}
                </div>
              ))}
            </DroppableContainer>
          </SortableContext>
          <SortableContext id="right-list" items={rightCards.map(c => c.id)} strategy={verticalListSortingStrategy}>
            <DroppableContainer id="right-list" style={{
              minHeight: 180,
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              background: isOverRight
                ? 'linear-gradient(135deg, #bbf7d0 0%, #f0fdf4 100%)'
                : 'linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%)',
              borderRadius: 18,
              border: isOverRight ? '3px dashed #a78bfa' : '3px dashed #a78bfa',
              padding: 32,
              paddingBottom: 90,
              boxShadow: isOverRight
                ? '0 8px 32px 0 rgba(168,139,250,0.18), 0 2px 8px 0 rgba(168,139,250,0.10)'
                : justDropped
                  ? '0 0 0 4px #a78bfa, 0 4px 24px 0 rgba(168,139,250,0.08)'
                  : '0 4px 24px 0 rgba(16,185,129,0.08)',
              transition: 'box-shadow 0.3s, border-color 0.3s, background 0.3s',
              position: 'relative',
              overflow: 'visible',
              animation: justDropped ? 'flash-purple 0.7s' : undefined,
            }} justDropped={justDropped}>
              <div className="flex flex-col items-center justify-center w-full mb-4">
                <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg" className={isOverRight ? 'animate-pulse' : ''}>
                  <rect x="3" y="7" width="32" height="22" rx="6" fill="url(#drop-gradient)" stroke="#22c55e" strokeWidth="2"/>
                  <rect x="3" y="13" width="32" height="4" fill="#bbf7d0"/>
                  <circle cx="10" cy="25" r="3" fill="#fbbf24"/>
                  <rect x="16" y="23" width="12" height="3" rx="1.5" fill="#fff" opacity="0.7"/>
                  <defs>
                    <linearGradient id="drop-gradient" x1="3" y1="7" x2="35" y2="29" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#6ee7b7"/>
                      <stop offset="1" stopColor="#bbf7d0"/>
                    </linearGradient>
                  </defs>
                </svg>
                <h4 className="font-bold text-green-700 text-lg mt-2 text-center">Cards to Assign</h4>
                <span className="text-xs text-green-600 mt-1 text-center">Drop cards here to assign</span>
              </div>
              <div className="flex flex-col w-full items-center justify-center">
                {rightCards.map((card) => (
                  <div key={card.id} className="flex items-center w-full mb-2 gap-2">
                       <DraggableCard
                        card={card}
                        id={card.id}
                        from="right"
                        setLeftCards={setLeftCards}
                        setRightCards={setRightCards}
                        isOverlay={false}
                        justDropped={justDroppedCardId === card.id}
                        selected={selectedCardIds.includes(card.id)}
                        onSelect={() => setSelectedCardIds(ids => ids.includes(card.id) ? ids.filter(i => i !== card.id) : [...ids, card.id])}
                      />
                    <button
                      className="text-xs text-red-500 hover:text-red-700 flex items-center justify-center p-2 rounded-full bg-red-50 hover:bg-red-100 transition"
                      title="Remove"
                      type="button"
                      tabIndex={0}
                      onClick={e => {
                        e.stopPropagation();
                        e.preventDefault();
                        setRightCards(prev => prev.filter(c => c.id !== card.id));
                        setLeftCards(prev => [...prev, card]);
                      }}
                      onPointerDown={e => e.stopPropagation()}
                      onMouseDown={e => e.stopPropagation()}
                      onTouchStart={e => e.stopPropagation()}
                      style={{ minWidth: 36, minHeight: 36 }}
                    >
                      <TfiTrash size={16} />
                    </button>
                  </div>
                ))}
                {rightCards.length === 0 && <div className="text-center text-gray-400 w-full">Drag cards here to assign</div>}
                {rightCards.length > 0 && (
                  <button
                    className="assign-cards-btn absolute left-6 right-6 bottom-6 mx-auto w-full md:w-auto py-3 px-8 rounded-2xl bg-gradient-to-r from-purple-500 to-green-400 text-white font-extrabold text-lg shadow-xl hover:from-purple-600 hover:to-green-600 transition-all duration-200 flex items-center justify-center gap-2 z-50"
                    style={{ maxWidth: 340 }}
                    onClick={handleAssignCards}
                    disabled={assignLoading}
                  >
                    <span className="pi pi-check-circle text-2xl mr-2" />
                    {assignLoading ? 'Assigning...' : 'Assign Cards'}
                  </button>
                )}
              </div>
              <style>{`
                @keyframes flash-green {
                  0% { box-shadow: 0 0 0 0 #22c55e; }
                  40% { box-shadow: 0 0 0 8px #bbf7d0; }
                  100% { box-shadow: 0 0 0 0 #22c55e; }
                }
                @keyframes flash-purple {
                  0% { box-shadow: 0 0 0 0 #a78bfa; }
                  40% { box-shadow: 0 0 0 8px #d3bff5; }
                  100% { box-shadow: 0 0 0 0 #a78bfa; }
                }
              `}</style>
            </DroppableContainer>
          </SortableContext>
        </div>
        <DragOverlay>
          {activeCardIds.length > 1 ? (
            <div className="p-4 bg-purple-100 rounded-xl shadow-lg font-bold text-purple-700 flex items-center gap-2">
              <span className="pi pi-clone text-2xl" />
              {activeCardIds.length} Cards
            </div>
          ) : activeCard ? (
            <DraggableCard
              card={activeCard}
              id={activeCard.id}
              from={rightCards.find(c => c.id === activeCard.id) ? 'right' : 'left'}
              setLeftCards={setLeftCards}
              setRightCards={setRightCards}
              isOverlay={true}
              justDropped={justDroppedCardId === activeCard.id}
              selected={selectedCardIds.includes(activeCard.id)}
              onSelect={() => setSelectedCardIds(ids => ids.includes(activeCard.id) ? ids.filter(i => i !== activeCard.id) : [...ids, activeCard.id])}
            />
          ) : null}
        </DragOverlay>
      </DndContext>
    </>
  );
}

CardsDnDSection.propTypes = {
  leftCards: PropTypes.array.isRequired,
  setLeftCards: PropTypes.func.isRequired,
  rightCards: PropTypes.array.isRequired,
  setRightCards: PropTypes.func.isRequired,
  cardsLoading: PropTypes.bool.isRequired,
  assignLoading: PropTypes.bool.isRequired,
  handleAssignCards: PropTypes.func.isRequired
};
<style>{`
@keyframes fade-in-up {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}
.animate-fade-in-up {
  animation: fade-in-up 0.25s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
@media print {
  @page { margin: 1cm; }
  html, body { margin: 0 !important; padding: 0 !important; }
  .print-hide-details { display: none !important; }
}
`}</style>