import { useDesignSpace } from "@contexts/DesignSpaceContext";
import { useState } from "react";

const icons = [
  "https://file-tech-test.livaatverse.com/uploads/file---1733395522.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395672.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395711.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395786.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396147.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396169.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395845.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395869.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395897.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395925.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395957.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396014.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396030.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396048.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396065.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396086.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396110.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396128.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396344.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396361.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396381.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396398.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395975.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395993.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396212.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396261.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396229.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396415.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395813.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396432.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396279.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396294.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396320.svg"
]

const iconColors = [
  '#000000', '#ffffff', '#FF0000', '#FF8000', '#FFFF00', '#80FF00', '#00FF00', '#00FF80', '#00FFFF', '#0080FF', '#0000FF', '#8000FF', '#FF00FF', '#FF0080',
  '#FF6B6B', '#FF8E53', '#FFA726', '#FFB74D', '#FFCC02', '#FFD54F', '#FFE082', '#FFECB3', '#FFF3E0', '#FFAB91', '#FF8A65', '#FF7043',
  '#4FC3F7', '#29B6F6', '#03A9F4', '#039BE5', '#0288D1', '#0277BD', '#01579B', '#E1F5FE', '#B3E5FC', '#81D4FA', '#8BC34A', '#CDDC39', '#FFEB3B', '#FF9800', '#FF5722', '#795548', '#9E9E9E', '#607D8B', '#3F51B5', '#2196F3', '#00BCD4', '#009688', '#FF1744', '#F50057', '#D500F9', '#651FFF', '#3D5AFE', '#2979FF', '#00B0FF', '#00E5FF', '#1DE9B6', '#00E676', '#76FF03', '#C6FF00'
];

function replaceSvgColor(svgText, color) {
  // استبدل جميع fill وstroke
  let out = svgText.replace(/fill="(.*?)"/gi, `fill="${color}"`);
  out = out.replace(/stroke="(.*?)"/gi, `stroke="${color}"`);
  // إذا لم يوجد fill في العنصر الرئيسي أضف fill للـ <svg>
  out = out.replace(/<svg([^>]*)>/i, (match, attrs) => {
    if (!/fill=/.test(attrs)) {
      return `<svg${attrs} fill="${color}">`;
    }
    return match;
  });
  return out;
}

function IconsSettings() {
  const { addElement } = useDesignSpace();
  const [iconColor, setIconColor] = useState('#000000');

  // عند الضغط على أيقونة: جلب SVG وتلوينه ثم إضافته كـ SVG
  const handleAddIcon = async (iconUrl) => {
    try {
      const res = await fetch(iconUrl);
      let svgText = await res.text();
      svgText = replaceSvgColor(svgText, iconColor);
      addElement('svg', svgText, {
        width: 100,
        height: 100,
        style: {},
        attributes: {}
      });
    } catch (e) {
      alert('فشل جلب أو تلوين الأيقونة');
    }
  };

  return (
    <div className="flex flex-col items-center w-full">
      {/* Color Picker */}
      <div className="flex flex-wrap gap-2 mb-4 items-center justify-center">
        {iconColors.map((color) => (
          <button
            key={color}
            type="button"
            className={`w-7 h-7 rounded-full border-2 transition-all ${iconColor === color ? 'border-blue-600 scale-110' : 'border-gray-300'}`}
            style={{ backgroundColor: color }}
            onClick={() => setIconColor(color)}
            aria-label={`Select color ${color}`}
          />
        ))}
        <input
          type="color"
          value={iconColor}
          onChange={e => setIconColor(e.target.value)}
          className="w-8 h-8 rounded border ml-2 cursor-pointer"
          aria-label="Custom color"
        />
        <input
          type="text"
          value={iconColor}
          onChange={e => setIconColor(e.target.value)}
          className="w-20 ml-2 px-2 py-1 border rounded text-xs font-mono"
          placeholder="#RRGGBB"
        />
      </div>
      {/* Icons Grid */}
      <div className="flex flex-wrap justify-start mx-auto">
        {icons.map((icon, index) => {
          return (
            <img
              loading="lazy"
              className="m-3 cursor-pointer"
              width={"30px"}
              key={index}
              src={icon}
              onClick={() => handleAddIcon(icon)}
              title="اضغط لإضافة الأيقونة باللون المختار"
              style={{ filter: `drop-shadow(0 0 0 ${iconColor})` }}
            />
          );
        })}
      </div>
    </div>
  );
}

export default IconsSettings;